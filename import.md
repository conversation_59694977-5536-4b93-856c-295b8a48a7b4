# 📦 MongoDB Data Import from Excel with Code Generation and Relational Mapping

## 🧾 Problem Overview

I have a MongoDB project using **Mongoose in Node.js**, and I want to import data from an Excel `.xlsx` file into a collection called `Tool` (or `Material`). The Excel file contains human-readable data, while the database schema uses ObjectIds for related fields and includes an autogenerated code field.

---

## 📥 Excel Input Format

The Excel file contains data structured like this:

| designation           | type | emplacement | location | reference       | dateReference | responsible    | direction |
| --------------------- | ---- | ----------- | -------- | --------------- | ------------- | -------------- | --------- |
| agrafeuse pneumatique | Com  | ATL Alum    | C1-5     | inventaire 2025 | 14/04/2025    | <PERSON><PERSON> <PERSON><PERSON> | DGMRE     |

> ❗ The file does **not** include the `codeGenerator`, and `emplacement`, `location`, and `responsible` are written as strings, not ObjectIds.

---

## 🗃️ MongoDB Schema Structure

### Tool Schema (Main Collection)

```js
const toolSchema = new mongoose.Schema({
  designation: String,
  codeGenerator: String, // generated like "HA001"
  type: String,
  emplacement: { type: mongoose.Schema.Types.ObjectId, ref: "Emplacement" },
  location: { type: mongoose.Schema.Types.ObjectId, ref: "Location" },
  reference: String,
  dateReference: Date,
  responsible: { type: mongoose.Schema.Types.ObjectId, ref: "Responsible" },
  direction: String,
});
```

### Related Schemas

const emplacementSchema = new mongoose.Schema({ name: String });
const locationSchema = new mongoose.Schema({ name: String });
const responsibleSchema = new mongoose.Schema({ name: String });

### Code Generator Rules

The codeGenerator must be generated dynamically for each row:

Take the first two letters of the designation, uppercase.

Count how many documents already exist for that prefix.

take a look into generateToken.js

Return code in format XX001, XX002, etc.

Example
designation: hammer

Result: HA001, then HA002, etc.

### Required Script Features

The Node.js script should:

Read data from an Excel file using xlsx.

For each row:

Generate a codeGenerator value.

Resolve or create emplacement, location, and responsible using their names (match by name, get ObjectId).

Insert all data into the Tool collection.

Either one-by-one or using insertMany for performance.

### Expected MongoDB Output
{
  "_id": {
    "$oid": "68529c974d6ae5ea2fdc6b27"
  },
  "designation": "AGRAFEUSE PNEUMATIQUE",
  "mat": "AG001",
  "acquisitionType": "M11",
  "acquisitionRef": "inventaire 2025",
  "acquisitionDate": {
    "$date": "2025-06-18T00:00:00.000Z"
  },
  "originalQte": 3,
  "currentQte": 3,
  "situation": "available",
  "type": "common",
  "direction": "DGMRE",
  "responsible": {
    "$oid": "68528a5b675cd7696c469623"
  },
  "location": {
    "$oid": "68528be2675cd7696c46964e"
  },
  "placement": {
    "$oid": "6852943552b6b1c2a853ebe1"
  },
  "history": [
    {
      "eventType": "entry",
      "reference": "m11-inventaire 2025",
      "date": {
        "$date": "2025-06-18T00:00:00.000Z"
      },
      "qteChange": 3,
      "notes": "Initial M11 acquisition",
      "performedBy": "system",
      "_id": {
        "$oid": "68529c974d6ae5ea2fdc6b28"
      }
    }
  ],
  "exits": [],
  "createdAt": {
    "$date": "2025-06-18T11:01:43.192Z"
  },
  "updatedAt": {
    "$date": "2025-06-18T11:01:43.192Z"
  },
  "__v": 0
}