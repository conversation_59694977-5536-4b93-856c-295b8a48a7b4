{"metadata": {"exportDate": "2025-06-19T08:30:00.000Z", "totalTools": 1, "excelFile": "liste des équipement.xlsx", "instructions": "This JSON contains tool data ready for manual import into MongoDB. Replace this sample data with your actual Excel data."}, "relatedEntities": {"responsibles": [{"name": "<PERSON><PERSON>", "grade": "Cne"}], "locations": [{"name": "C1-5", "description": "Auto-created during Excel import"}], "placements": [{"name": "ATL Alum", "description": "Auto-created during Excel import"}]}, "tools": [{"designation": "AGRAFEUSE PNEUMATIQUE", "mat": "AG001", "acquisitionType": "M11", "acquisitionRef": "inventaire 2025", "acquisitionDate": "2025-04-14T00:00:00.000Z", "originalQte": 1, "currentQte": 1, "responsible": "<PERSON><PERSON>", "location": "C1-5", "placement": "ATL Alum", "type": "common", "direction": "DGMRE", "situation": "available", "notes": "Imported from Excel on 2025-06-19T08:30:00.000Z", "history": [{"eventType": "entry", "reference": "m11-inventaire 2025", "date": "2025-04-14T00:00:00.000Z", "qteChange": 1, "notes": "Initial M11 acquisition from Excel import", "performedBy": "system"}], "exits": []}]}