# Excel Import Scripts

This directory contains scripts for importing tool/equipment data from Excel files into the MongoDB Tooling collection.

## Files

- `importExcel.js` - Main import script that reads Excel data and imports into database
- `importHelpers.js` - Helper functions for data processing and entity resolution
- `runImport.js` - Simple runner script for executing the import
- `README.md` - This documentation file

## Prerequisites

1. **Environment Setup**: Ensure your `.env` file contains the correct `MONGO_URI`
2. **Excel File**: Place your Excel file at `frontend/src/data/liste des équipement.xlsx`
3. **Dependencies**: The script uses `exceljs` which should already be installed

## Excel File Format

The Excel file should contain the following columns (case-insensitive):

| Column | Description | Required | Example |
|--------|-------------|----------|---------|
| designation | Tool/equipment name | Yes | "AGRAFEUSE PNEUMATIQUE" |
| type | Tool type | Yes | "Com" (maps to "common") |
| emplacement | Placement/location within facility | No | "ATL Alum" |
| location | General location | No | "C1-5" |
| reference | Acquisition reference | No | "inventaire 2025" |
| dateReference | Acquisition date | No | "14/04/2025" |
| responsible | Responsible person | No | "Cne Rami Dalli" |
| direction | Direction/department | Yes | "DGMRE" |

### Type Mapping

Excel values are automatically mapped to schema enum values:
- `"Com"` or `"common"` → `"common"`
- `"Cal"` or `"calibration"` → `"calibration"`
- `"Main"` or `"maintenance"` → `"maintenance"`
- `"Did"` or `"didactic"` → `"didactic"`

### Direction Values

Valid directions: `DGMRE`, `DGTI`, `DGGM`, `DHS`, `DASIC`

## Usage

### Method 1: Using the Runner Script (Recommended)

```bash
# From the project root directory
node backend/scripts/runImport.js
```

### Method 2: Direct Execution

```bash
# From the project root directory
node backend/scripts/importExcel.js
```

### Method 3: Programmatic Usage

```javascript
import importExcelData from './backend/scripts/importExcel.js';

const result = await importExcelData();
if (result.success) {
  console.log('Import successful:', result.summary);
} else {
  console.error('Import failed:', result.error);
}
```

## How It Works

1. **File Reading**: Uses ExcelJS to read the Excel file
2. **Header Detection**: Automatically detects column headers (case-insensitive)
3. **Data Processing**: For each row:
   - Extracts data from Excel cells
   - Validates and maps type values
   - Resolves or creates related entities (Responsible, Location, Placement)
   - Generates MAT code automatically via schema pre-save hook
4. **Database Operations**: 
   - Checks for existing tools to avoid duplicates
   - Creates new Tooling documents with proper relationships
   - Adds initial history entry for tracking

## Features

- **Duplicate Prevention**: Skips tools that already exist (by designation)
- **Auto-Entity Creation**: Creates Responsible, Location, and Placement entities if they don't exist
- **MAT Code Generation**: Automatically generates unique MAT codes (e.g., AG001, AG002)
- **Comprehensive Logging**: Detailed progress and error reporting
- **Error Handling**: Continues processing even if individual rows fail
- **Data Validation**: Validates dates, directions, and other fields
- **Military Rank Detection**: Automatically extracts military ranks from responsible names

## Output

The script provides detailed output including:
- Progress updates for each row
- Summary of results (success, skipped, errors)
- Execution time
- Error details for failed rows
- List of successfully imported tools

## Error Handling

- **File Not Found**: Clear error if Excel file doesn't exist
- **Invalid Data**: Continues processing, logs errors for individual rows
- **Database Errors**: Proper error handling and connection cleanup
- **Schema Validation**: Mongoose validation errors are caught and reported

## Troubleshooting

### Common Issues

1. **File Not Found**
   - Ensure Excel file is at `frontend/src/data/liste des équipement.xlsx`
   - Check file permissions

2. **Database Connection**
   - Verify `MONGO_URI` in `.env` file
   - Ensure MongoDB is running

3. **Column Not Found**
   - Check Excel headers match expected names
   - Headers are case-insensitive but should be recognizable

4. **Duplicate Tools**
   - Tools with same designation are skipped
   - Check existing data if unexpected skips occur

### Debug Mode

For more detailed debugging, you can modify the script to add console.log statements or use a debugger.

## Schema Mapping

The script maps Excel data to the actual Tooling schema:

```javascript
// Excel → Database Schema
designation → designation
type → type (with mapping)
emplacement → placement (ObjectId reference)
location → location (ObjectId reference)  
reference → acquisitionRef
dateReference → acquisitionDate
responsible → responsible (ObjectId reference)
direction → direction

// Auto-generated fields
mat → Generated automatically (e.g., AG001)
acquisitionType → "M11" (default for inventory)
originalQte → 1 (default)
currentQte → 1 (default)
situation → "available" (default)
history → Initial entry record
```
